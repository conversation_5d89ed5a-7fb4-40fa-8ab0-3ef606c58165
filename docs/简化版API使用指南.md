# 移动APP检查清单API - 简化版使用指南

## 🎯 设计理念

基于您的反馈，我们重新设计了API接口，**最大化简化用户操作**：

- ✅ **只需要项目标题** - 其他信息都是可选的
- ✅ **智能默认值** - 系统自动填充合理的默认值
- ✅ **灵活的模板选择** - 支持选择多个模板或不选择模板

## 📋 推荐的API使用流程

### 🚀 方案1：超简化创建（推荐）

**接口：** `POST /api/MobileApp/QuickCreateProject`

**最简请求（只需要标题）：**
```json
{
  "projectTitle": "我的新项目"
}
```

**带模板选择的请求：**
```json
{
  "projectTitle": "我的新项目",
  "templateIds": [1, 2]
}
```

**特点：**
- 🎯 **极简操作**：只需要填写项目标题
- 🤖 **智能处理**：
  - 如果不选择模板 → 自动创建默认检查清单（基础设施检查、安全设施检查、环境质量检查）
  - 如果选择模板 → 基于选中的模板创建检查清单
- 📱 **移动端友好**：最适合移动APP的简化操作

### 🔧 方案2：获取模板后选择

1. **获取模板列表：** `GET /api/MobileApp/GetChecklistTemplates`
2. **用户选择模板**
3. **快速创建项目：** `POST /api/MobileApp/QuickCreateProject`

## 📊 模板选择机制说明

### 问题：如何确定用户选了哪些模板？

**解决方案：**

1. **获取可用模板**
   ```http
   GET /api/MobileApp/GetChecklistTemplates
   ```
   
2. **用户在界面上选择模板**（多选）
   - 显示模板列表
   - 用户可以选择0个、1个或多个模板
   - 每个模板显示：模板名称、包含的检查项数量

3. **提交选择的模板ID**
   ```json
   {
     "projectTitle": "项目标题",
     "templateIds": [1, 3, 5]  // 用户选择的模板ID数组
   }
   ```

### 🎨 移动端UI建议

```
┌─────────────────────────────────┐
│ 创建新项目                        │
├─────────────────────────────────┤
│ 项目标题: [_______________] *     │
│                                 │
│ 选择检查清单模板（可选）:          │
│ ☐ 标准检查清单 (5项)             │
│ ☑ 安全检查清单 (3项)             │
│ ☐ 环境检查清单 (4项)             │
│ ☑ 质量检查清单 (6项)             │
│                                 │
│ 项目描述（可选）:                 │
│ [_________________________]    │
│                                 │
│ [创建项目] [取消]                │
└─────────────────────────────────┘
```

## 🔄 完整的测试数据

### 1. 获取模板列表
```http
GET {{baseUrl}}/api/MobileApp/GetChecklistTemplates
Authorization: Bearer {{token}}
```

### 2. 快速创建项目（无模板）
```json
{
  "projectTitle": "测试项目1"
}
```

### 3. 快速创建项目（单个模板）
```json
{
  "projectTitle": "测试项目2",
  "templateIds": [1]
}
```

### 4. 快速创建项目（多个模板）
```json
{
  "projectTitle": "测试项目3",
  "templateIds": [1, 2, 3]
}
```

### 5. 快速创建项目（带描述）
```json
{
  "projectTitle": "测试项目4",
  "projectDescription": "这是一个详细的项目描述",
  "templateIds": [1]
}
```

## ✅ 预期响应

所有创建接口都返回相同格式：

```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "测试项目",
    "checklistId": 456,
    "checklistName": "检查清单名称",
    "checklistItems": [
      {
        "id": 789,
        "title": "检查项目1",
        "sortOrder": 1,
        "status": null
      }
    ]
  }
}
```

## 🎯 业务逻辑说明

### 默认行为
- **项目标题**：必填，用户输入
- **项目描述**：可选，用户输入或为空
- **检查员**：自动设置为当前登录用户
- **公司**：自动设置为当前用户的公司
- **创建时间**：自动设置为当前时间

### 检查清单创建逻辑
1. **无模板选择**：创建"默认检查清单"，包含3个基础检查项
2. **单个模板**：基于模板创建检查清单，使用模板名称
3. **多个模板**：为每个模板创建一个检查清单，检查清单名称为模板名称

### 错误处理
- 模板不存在 → 返回错误信息
- 模板不属于当前公司 → 返回权限错误
- 项目标题为空 → 返回验证错误

## 🚀 推荐实现步骤

1. **第一步**：实现最简单的创建（只有标题）
2. **第二步**：添加模板列表获取功能
3. **第三步**：添加模板选择功能
4. **第四步**：优化用户界面和体验

这样的设计既满足了"只需要填写项目标题"的需求，又提供了灵活的模板选择机制！
