# 移动APP检查清单相关API文档

## 概述

为了简化移动APP用户的操作流程，我们新增了三个API接口，允许用户直接创建检查清单并自动生成项目，无需先手动创建项目。

## 新增接口

### 1. 获取检查清单模板列表

**接口地址：** `GET /api/MobileApp/GetChecklistTemplates`

**功能描述：** 获取公司的检查清单模板列表，供用户选择

**请求参数：** 无（从认证用户获取公司ID）

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "ListOfTemplates": [
    {
      "id": 1,
      "title": "标准检查清单模板",
      "isDefault": true,
      "sortOrder": 1,
      "items": [
        {
          "id": 1,
          "title": "检查项目1",
          "sortOrder": 1
        },
        {
          "id": 2,
          "title": "检查项目2",
          "sortOrder": 2
        }
      ]
    }
  ]
}
```

### 2. 创建检查清单并自动生成项目

**接口地址：** `POST /api/MobileApp/CreateChecklistWithProject`

**功能描述：** 用户提供项目信息和检查清单详情，系统自动创建项目和检查清单

**请求参数：**
```json
{
  "projectTitle": "新建项目标题",
  "projectDescription": "项目描述",
  "address": "项目地址",
  "postNo": "邮编",
  "poststed": "邮政地址",
  "kommune": "市镇",
  "comments": "备注",
  "customerId": 1,
  "contactPersonId": 1,
  "longitude": "经度",
  "latitude": "纬度",
  "checklistName": "检查清单名称",
  "checklistComment": "检查清单备注",
  "checklistItems": [
    {
      "title": "检查项目1",
      "sortOrder": 1
    },
    {
      "title": "检查项目2",
      "sortOrder": 2
    }
  ]
}
```

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "新建项目标题",
    "checklistId": 456,
    "checklistName": "检查清单名称",
    "checklistItems": [
      {
        "id": 789,
        "title": "检查项目1",
        "sortOrder": 1,
        "status": null
      }
    ]
  }
}
```

### 3. 基于模板创建检查清单并自动生成项目

**接口地址：** `POST /api/MobileApp/CreateChecklistFromTemplate`

**功能描述：** 用户选择模板并提供项目信息，系统基于模板自动创建项目和检查清单

**请求参数：**
```json
{
  "projectTitle": "基于模板的新项目",
  "projectDescription": "项目描述",
  "address": "项目地址",
  "postNo": "邮编",
  "poststed": "邮政地址",
  "kommune": "市镇",
  "comments": "备注",
  "customerId": 1,
  "contactPersonId": 1,
  "longitude": "经度",
  "latitude": "纬度",
  "templateId": 1,
  "checklistName": "自定义检查清单名称（可选）",
  "checklistComment": "检查清单备注"
}
```

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 124,
    "projectTitle": "基于模板的新项目",
    "checklistId": 457,
    "checklistName": "标准检查清单模板",
    "checklistItems": [
      {
        "id": 790,
        "title": "模板检查项目1",
        "sortOrder": 1,
        "status": null
      },
      {
        "id": 791,
        "title": "模板检查项目2",
        "sortOrder": 2,
        "status": null
      }
    ]
  }
}
```

## 业务流程

### 方案1：自定义检查清单
1. 用户填写项目基本信息
2. 用户自定义检查清单名称和检查项
3. 调用 `CreateChecklistWithProject` 接口
4. 系统自动创建项目和检查清单
5. 返回创建结果

### 方案2：基于模板创建
1. 用户调用 `GetChecklistTemplates` 获取模板列表
2. 用户选择合适的模板
3. 用户填写项目基本信息
4. 调用 `CreateChecklistFromTemplate` 接口
5. 系统基于模板自动创建项目和检查清单
6. 返回创建结果

## 错误处理

所有接口在发生错误时返回统一格式：
```json
{
  "Response": {
    "Code": "100",
    "Message": "错误描述信息"
  }
}
```

## 权限要求

- 所有接口都需要用户认证
- 用户只能访问自己公司的模板和创建自己公司的项目
- 系统会自动设置当前用户为项目的检查员

## 技术实现特点

1. **事务性保证**：项目和检查清单的创建在同一事务中，确保数据一致性
2. **最小代码改动**：复用现有的项目创建和检查清单创建逻辑
3. **错误回滚**：如果任何步骤失败，整个操作会回滚
4. **灵活性**：支持自定义检查清单和基于模板两种方式
5. **扩展性**：可以轻松添加更多项目属性和检查清单配置
