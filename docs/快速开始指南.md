# 移动APP检查清单API - 快速开始指南

## 🚀 5分钟快速上手

### 第1步：导入Postman测试文件

1. 打开Postman
2. 点击 "Import" 按钮
3. 导入以下文件：
   - `postman/NBK_MobileApp_Checklist_APIs.postman_collection.json`
   - `postman/NBK_Test_Environment.postman_environment.json`

### 第2步：配置环境变量

在Postman环境中设置：
- `baseUrl`: `http://localhost:8080` (您的服务器地址)
- `token`: 从登录接口获取的JWT token

### 第3步：测试最简单的接口

**只需要项目标题即可创建项目！**

```json
POST {{baseUrl}}/api/MobileApp/QuickCreateProject
{
  "projectTitle": "我的第一个测试项目"
}
```

**预期响应：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "我的第一个测试项目",
    "checklistId": 456,
    "checklistName": "默认检查清单",
    "checklistItems": [
      {
        "id": 789,
        "title": "基础设施检查",
        "sortOrder": 1,
        "status": null
      },
      {
        "id": 790,
        "title": "安全设施检查",
        "sortOrder": 2,
        "status": null
      },
      {
        "id": 791,
        "title": "环境质量检查",
        "sortOrder": 3,
        "status": null
      }
    ]
  }
}
```

## 🎯 核心接口说明

### 1. 极简创建（推荐）

**最少输入：只需要项目标题**

```http
POST /api/MobileApp/QuickCreateProject
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "projectTitle": "项目标题"
}
```

**系统自动处理：**
- ✅ 创建项目
- ✅ 创建默认检查清单
- ✅ 添加3个基础检查项
- ✅ 设置当前用户为检查员

### 2. 模板选择创建

**先获取模板列表：**
```http
GET /api/MobileApp/GetChecklistTemplates
```

**然后选择模板创建：**
```json
{
  "projectTitle": "项目标题",
  "templateIds": [1, 2]
}
```

## 📱 移动端集成建议

### 最简UI设计
```
┌─────────────────────────────────┐
│ 快速创建项目                      │
├─────────────────────────────────┤
│                                 │
│ 项目标题 *                       │
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        立即创建              │ │
│ └─────────────────────────────┘ │
│                                 │
└─────────────────────────────────┘
```

### 代码示例（JavaScript/React Native）

```javascript
// 极简创建项目
const createProject = async (title) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/MobileApp/QuickCreateProject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({
        projectTitle: title
      })
    });
    
    const result = await response.json();
    
    if (result.Response.Code === "200") {
      // 创建成功
      console.log('项目创建成功:', result.Data);
      return result.Data;
    } else {
      // 创建失败
      console.error('创建失败:', result.Response.Message);
      throw new Error(result.Response.Message);
    }
  } catch (error) {
    console.error('网络错误:', error);
    throw error;
  }
};

// 使用示例
createProject("我的新项目")
  .then(projectData => {
    console.log(`项目ID: ${projectData.projectId}`);
    console.log(`检查清单ID: ${projectData.checklistId}`);
    // 跳转到项目详情页面
  })
  .catch(error => {
    // 显示错误提示
    alert('创建项目失败: ' + error.message);
  });
```

## 🔧 常见问题解答

### Q1: 用户必须填写哪些信息？
**A:** 只需要填写项目标题，其他都是可选的。

### Q2: 如果不选择模板会怎样？
**A:** 系统会自动创建一个默认检查清单，包含3个基础检查项。

### Q3: 可以选择多个模板吗？
**A:** 可以，系统会为每个模板创建一个检查清单。

### Q4: 创建失败会怎样？
**A:** 所有操作都在事务中，失败时会自动回滚，不会产生脏数据。

### Q5: 如何验证创建是否成功？
**A:** 检查响应中的`Response.Code`是否为"200"，并且`Data`中包含项目和检查清单信息。

## 🎯 最佳实践

### 1. 错误处理
```javascript
if (result.Response.Code !== "200") {
  // 显示用户友好的错误信息
  showError(result.Response.Message);
  return;
}
```

### 2. 加载状态
```javascript
setLoading(true);
try {
  const project = await createProject(title);
  // 处理成功结果
} catch (error) {
  // 处理错误
} finally {
  setLoading(false);
}
```

### 3. 数据验证
```javascript
if (!title || title.trim().length === 0) {
  alert('请输入项目标题');
  return;
}

if (title.length > 100) {
  alert('项目标题不能超过100个字符');
  return;
}
```

## 📈 下一步

1. **测试基础功能**：使用Postman测试所有接口
2. **集成到移动端**：根据UI设计集成API
3. **添加错误处理**：完善用户体验
4. **性能优化**：根据实际使用情况优化

## 🆘 获取帮助

如果遇到问题：
1. 检查服务器是否正常运行
2. 确认认证token是否有效
3. 查看服务器日志获取详细错误信息
4. 参考完整的API文档：`docs/移动APP检查清单API完整指南.md`

**恭喜！您已经掌握了移动APP检查清单API的基本使用方法！** 🎉
