# 移动APP检查清单API - 完整指南

## 📋 概述

为了简化移动APP用户的操作流程，我们新增了检查清单相关API接口，允许用户直接创建检查清单并自动生成项目，无需先手动创建项目。

### 🎯 核心特性
- ✅ **极简操作**：最少只需填写项目标题
- ✅ **智能默认**：系统自动处理必要信息
- ✅ **灵活模板**：支持选择多个模板或使用默认模板
- ✅ **事务保证**：项目和检查清单创建在同一事务中

## 🚀 新增API接口

### 1. 获取检查清单模板列表

**接口地址：** `GET /api/MobileApp/GetChecklistTemplates`

**功能描述：** 获取公司的检查清单模板列表，供用户选择

**请求参数：** 无（从认证用户获取公司ID）

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "ListOfTemplates": [
    {
      "id": 1,
      "title": "标准检查清单模板",
      "isDefault": true,
      "sortOrder": 1,
      "items": [
        {
          "id": 1,
          "title": "基础设施检查",
          "sortOrder": 1
        },
        {
          "id": 2,
          "title": "安全设施检查",
          "sortOrder": 2
        }
      ]
    }
  ]
}
```

### 2. 快速创建项目（推荐）

**接口地址：** `POST /api/MobileApp/QuickCreateProject`

**功能描述：** 最简化的项目创建接口，只需要项目标题，可选择模板

**请求参数：**
```json
{
  "projectTitle": "我的新项目",           // 必填：项目标题
  "templateIds": [1, 2],               // 可选：选择的模板ID数组
  "projectDescription": "项目描述"      // 可选：项目描述
}
```

**最简请求（只需标题）：**
```json
{
  "projectTitle": "我的新项目"
}
```

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "我的新项目",
    "checklistId": 456,
    "checklistName": "默认检查清单",
    "checklistItems": [
      {
        "id": 789,
        "title": "基础设施检查",
        "sortOrder": 1,
        "status": null
      }
    ]
  }
}
```

### 3. 创建自定义检查清单项目

**接口地址：** `POST /api/MobileApp/CreateChecklistWithProject`

**功能描述：** 用户自定义检查清单详情并创建项目

**请求参数：**
```json
{
  "projectTitle": "自定义项目",
  "checklistName": "自定义检查清单",
  "checklistItems": [
    {
      "title": "检查项目1",
      "sortOrder": 1
    }
  ]
}
```

### 4. 基于模板创建项目

**接口地址：** `POST /api/MobileApp/CreateChecklistFromTemplate`

**功能描述：** 基于选择的模板创建项目和检查清单

**请求参数：**
```json
{
  "projectTitle": "基于模板的项目",
  "templateIds": [1, 2],
  "checklistName": "自定义名称"
}
```

## 📱 推荐使用流程

### 🎯 方案1：极简创建（最推荐）

适用场景：用户只想快速创建项目，不关心具体的检查项

```
用户输入项目标题 → 点击创建 → 完成
```

**API调用：**
```json
POST /api/MobileApp/QuickCreateProject
{
  "projectTitle": "快速项目"
}
```

**系统行为：**
- 自动创建项目
- 自动创建"默认检查清单"
- 自动添加3个基础检查项（基础设施、安全设施、环境质量）

### 🎯 方案2：模板选择创建

适用场景：用户想使用预定义的检查清单模板

```
获取模板列表 → 用户选择模板 → 输入项目标题 → 创建项目
```

**API调用流程：**
```json
// 1. 获取模板
GET /api/MobileApp/GetChecklistTemplates

// 2. 创建项目
POST /api/MobileApp/QuickCreateProject
{
  "projectTitle": "模板项目",
  "templateIds": [1, 3]
}
```

## 🧪 Postman测试指南

### 📁 导入测试文件

1. **导入Collection**：`NBK_MobileApp_Checklist_APIs.postman_collection.json`
2. **导入Environment**：`NBK_Test_Environment.postman_environment.json`

### ⚙️ 环境配置

在Postman环境中设置以下变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `baseUrl` | API服务器地址 | `http://localhost:8080` |
| `token` | JWT认证令牌 | `eyJhbGciOiJIUzI1NiIs...` |

### 🔄 测试顺序

#### 1️⃣ 获取认证Token
使用现有登录接口获取token，设置到环境变量中

#### 2️⃣ 获取模板列表
```http
GET {{baseUrl}}/api/MobileApp/GetChecklistTemplates
Authorization: Bearer {{token}}
```

#### 3️⃣ 快速创建项目（无模板）
```json
POST {{baseUrl}}/api/MobileApp/QuickCreateProject
{
  "projectTitle": "测试项目1"
}
```

#### 4️⃣ 快速创建项目（带模板）
```json
POST {{baseUrl}}/api/MobileApp/QuickCreateProject
{
  "projectTitle": "测试项目2",
  "templateIds": [1]
}
```

#### 5️⃣ 验证创建结果
```http
GET {{baseUrl}}/api/MobileApp/GetProjectList
```

### 📊 测试数据示例

#### 极简测试数据
```json
{
  "projectTitle": "极简测试项目"
}
```

#### 完整测试数据
```json
{
  "projectTitle": "完整测试项目",
  "projectDescription": "这是一个完整的测试项目",
  "templateIds": [1, 2, 3]
}
```

#### 自定义检查清单测试数据
```json
{
  "projectTitle": "自定义项目",
  "checklistName": "自定义检查清单",
  "checklistItems": [
    {
      "title": "自定义检查项1",
      "sortOrder": 1
    },
    {
      "title": "自定义检查项2", 
      "sortOrder": 2
    }
  ]
}
```

## 🎨 移动端UI设计建议

### 极简创建界面
```
┌─────────────────────────────────┐
│ 快速创建项目                      │
├─────────────────────────────────┤
│ 项目标题: [_______________] *     │
│                                 │
│ [立即创建]                       │
└─────────────────────────────────┘
```

### 模板选择界面
```
┌─────────────────────────────────┐
│ 创建新项目                        │
├─────────────────────────────────┤
│ 项目标题: [_______________] *     │
│                                 │
│ 选择检查清单模板（可选）:          │
│ ☐ 标准检查清单 (5项)             │
│ ☑ 安全检查清单 (3项)             │
│ ☐ 环境检查清单 (4项)             │
│ ☑ 质量检查清单 (6项)             │
│                                 │
│ 项目描述（可选）:                 │
│ [_________________________]    │
│                                 │
│ [创建项目] [取消]                │
└─────────────────────────────────┘
```

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| `100` | 用户未找到 | 检查认证token |
| `100` | 模板不存在 | 检查模板ID是否正确 |
| `100` | 权限不足 | 确认模板属于当前公司 |
| `400` | 参数验证失败 | 检查必填字段 |

### 错误响应格式
```json
{
  "Response": {
    "Code": "100",
    "Message": "具体错误描述"
  }
}
```

## 🔧 技术实现特点

1. **事务性保证**：项目和检查清单创建在同一事务中
2. **最小代码改动**：复用现有业务逻辑
3. **智能默认值**：自动填充必要信息
4. **灵活扩展**：支持多种创建方式
5. **错误回滚**：失败时自动回滚所有操作

## 📈 业务逻辑说明

### 默认行为
- **检查员**：自动设置为当前登录用户
- **公司**：自动设置为当前用户的公司
- **创建时间**：自动设置为当前时间
- **检查清单名称**：未提供时使用"默认检查清单"

### 模板处理逻辑
- **无模板**：创建默认检查清单（3个基础检查项）
- **单个模板**：基于模板创建检查清单
- **多个模板**：为每个模板创建一个检查清单

## 🎯 最佳实践建议

1. **优先使用QuickCreateProject**：最简单高效
2. **合理使用模板**：提高检查清单的标准化
3. **适当的错误处理**：提供友好的用户提示
4. **测试各种场景**：确保功能稳定性

这个整合的API设计既满足了极简操作的需求，又提供了足够的灵活性来应对不同的使用场景！
