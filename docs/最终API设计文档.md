# 移动APP检查清单API - 最终设计文档

## 📋 设计总结

根据您的需求，我们将API简化为**只有2个接口**，实现了极简的用户体验：

### 🎯 核心理念
- ✅ **极简输入**：只需要项目标题和检查清单名称
- ✅ **灵活选择**：可选择模板、自定义检查项，或两者结合
- ✅ **智能默认**：如果什么都不选择，自动创建默认检查项

## 🚀 API接口

### 1. 获取检查清单模板列表
```http
GET /api/MobileApp/GetChecklistTemplates
```
**功能：** 获取公司的检查清单模板列表，供用户选择

### 2. 创建项目和检查清单
```http
POST /api/MobileApp/CreateChecklistFromTemplate
```
**功能：** 创建项目和检查清单，支持多种创建方式

## 📝 请求参数设计

### CreateChecklistFromTemplateRequest
```json
{
  "projectTitle": "项目标题",           // 必填
  "checklistName": "检查清单名称",      // 必填
  "templateIds": [1, 2],              // 可选：模板ID数组
  "customChecklistItems": [           // 可选：自定义检查项
    {
      "title": "自定义检查项",
      "sortOrder": 1
    }
  ]
}
```

## 🎨 使用场景

### 场景1：最简创建（推荐）
```json
{
  "projectTitle": "我的项目",
  "checklistName": "我的检查清单"
}
```
**结果：** 自动创建3个默认检查项（基础设施、安全设施、环境质量）

### 场景2：选择模板
```json
{
  "projectTitle": "模板项目",
  "checklistName": "标准检查清单",
  "templateIds": [1, 2]
}
```
**结果：** 基于选择的模板创建检查项

### 场景3：自定义检查项
```json
{
  "projectTitle": "自定义项目",
  "checklistName": "自定义检查清单",
  "customChecklistItems": [
    {
      "title": "特殊检查项",
      "sortOrder": 1
    }
  ]
}
```
**结果：** 创建用户自定义的检查项

### 场景4：模板 + 自定义（最灵活）
```json
{
  "projectTitle": "混合项目",
  "checklistName": "混合检查清单",
  "templateIds": [1],
  "customChecklistItems": [
    {
      "title": "额外检查项",
      "sortOrder": 10
    }
  ]
}
```
**结果：** 既有模板检查项，又有自定义检查项

## 🔄 业务逻辑

### 检查项创建逻辑
1. **模板检查项**：从选择的模板中复制检查项
2. **自定义检查项**：添加用户自定义的检查项  
3. **默认检查项**：如果既没有模板也没有自定义项，创建默认检查项

### 排序规则
- 模板检查项：保持原有排序
- 自定义检查项：使用用户指定的排序，未指定则自动分配

### 自动处理
- **项目创建**：自动设置当前用户为检查员
- **公司关联**：自动关联到当前用户的公司
- **时间戳**：自动设置创建时间

## 📱 移动端集成建议

### UI设计
```
┌─────────────────────────────────┐
│ 创建新项目                        │
├─────────────────────────────────┤
│ 项目标题 *                       │
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 检查清单名称 *                   │
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 选择模板（可选）:                 │
│ ☐ 标准检查清单 (5项)             │
│ ☑ 安全检查清单 (3项)             │
│                                 │
│ [+ 添加自定义检查项]              │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        创建项目              │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### 代码示例
```javascript
// 最简创建
const createSimpleProject = async (title, checklistName) => {
  const response = await fetch('/api/MobileApp/CreateChecklistFromTemplate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      projectTitle: title,
      checklistName: checklistName
    })
  });
  return response.json();
};

// 带模板创建
const createProjectWithTemplates = async (title, checklistName, templateIds) => {
  const response = await fetch('/api/MobileApp/CreateChecklistFromTemplate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      projectTitle: title,
      checklistName: checklistName,
      templateIds: templateIds
    })
  });
  return response.json();
};
```

## 🧪 测试覆盖

### Postman测试集合
- ✅ 获取模板列表
- ✅ 最简创建（只有必填字段）
- ✅ 模板选择创建
- ✅ 自定义检查项创建
- ✅ 混合创建（模板 + 自定义）
- ✅ 验证创建结果

### 测试数据
提供了完整的测试数据，覆盖所有使用场景和边界情况。

## ✅ 实现优势

### 1. 用户体验
- **极简操作**：最少只需要2个字段
- **灵活选择**：支持多种创建方式
- **智能默认**：无需复杂配置

### 2. 技术实现
- **代码复用**：充分利用现有业务逻辑
- **事务保证**：确保数据一致性
- **错误处理**：完善的异常处理机制

### 3. 可维护性
- **接口简洁**：只有2个接口，易于维护
- **逻辑清晰**：业务逻辑简单明了
- **扩展性好**：可以轻松添加新功能

## 📊 对比原需求

### 原需求
- 用户需要先创建项目，再添加检查清单
- 填写大量项目信息
- 操作步骤复杂

### 现在的解决方案
- ✅ 一步完成项目和检查清单创建
- ✅ 只需要填写项目标题和检查清单名称
- ✅ 可选择模板或自定义检查项
- ✅ 智能默认处理

## 🎯 总结

这个简化版API设计完美解决了您提出的问题：

1. **减少用户输入**：从复杂的多字段输入简化为只需要2个必填字段
2. **灵活的模板选择**：通过`templateIds`数组明确指定选择的模板
3. **支持自定义**：可以添加自定义检查项
4. **智能默认**：什么都不选择时自动创建默认检查项

用户现在可以非常简单地创建项目：只需要输入项目标题和检查清单名称，可选择性地选择一些模板或添加自定义检查项，系统就会自动创建项目和相应的检查清单！
