# 移动APP检查清单API - 简化版接口文档

## 📋 概述

根据需求简化，现在只有**2个核心接口**，专为移动APP设计：

1. **获取模板列表** - `GET /api/MobileApp/GetChecklistTemplates`
2. **创建项目和检查清单** - `POST /api/MobileApp/CreateChecklistFromTemplate`

## 🎯 设计理念

- ✅ **极简输入**：只需要项目标题和检查清单名称
- ✅ **灵活选择**：可以选择模板、自定义检查项，或两者结合
- ✅ **智能默认**：如果什么都不选择，自动创建默认检查项

## 📋 API接口详情

### 1. 获取检查清单模板列表

**接口地址：** `GET /api/MobileApp/GetChecklistTemplates`

**功能描述：** 获取公司的检查清单模板列表

**请求参数：** 无（从认证用户获取公司ID）

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "ListOfTemplates": [
    {
      "id": 1,
      "title": "标准检查清单模板",
      "isDefault": true,
      "sortOrder": 1,
      "items": [
        {
          "id": 1,
          "title": "基础设施检查",
          "sortOrder": 1
        },
        {
          "id": 2,
          "title": "安全设施检查",
          "sortOrder": 2
        }
      ]
    }
  ]
}
```

### 2. 创建项目和检查清单

**接口地址：** `POST /api/MobileApp/CreateChecklistFromTemplate`

**功能描述：** 创建项目和检查清单，支持模板选择和自定义检查项

**请求参数：**
```json
{
  "projectTitle": "项目标题",           // 必填
  "checklistName": "检查清单名称",      // 必填
  "templateIds": [1, 2],              // 可选：选择的模板ID数组
  "customChecklistItems": [           // 可选：自定义检查项
    {
      "title": "自定义检查项1",
      "sortOrder": 10
    },
    {
      "title": "自定义检查项2",
      "sortOrder": 11
    }
  ]
}
```

**使用场景：**

#### 场景1：只使用模板
```json
{
  "projectTitle": "模板项目",
  "checklistName": "标准检查清单",
  "templateIds": [1, 2]
}
```

#### 场景2：只使用自定义检查项
```json
{
  "projectTitle": "自定义项目",
  "checklistName": "自定义检查清单",
  "customChecklistItems": [
    {
      "title": "自定义检查项1",
      "sortOrder": 1
    }
  ]
}
```

#### 场景3：模板 + 自定义检查项
```json
{
  "projectTitle": "混合项目",
  "checklistName": "混合检查清单",
  "templateIds": [1],
  "customChecklistItems": [
    {
      "title": "额外检查项",
      "sortOrder": 10
    }
  ]
}
```

#### 场景4：最简创建（使用默认检查项）
```json
{
  "projectTitle": "简单项目",
  "checklistName": "简单检查清单"
}
```

**响应示例：**
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "项目标题",
    "checklistId": 456,
    "checklistName": "检查清单名称",
    "checklistItems": [
      {
        "id": 789,
        "title": "基础设施检查",
        "sortOrder": 1,
        "status": null
      },
      {
        "id": 790,
        "title": "自定义检查项1",
        "sortOrder": 10,
        "status": null
      }
    ]
  }
}
```

## 🔄 业务逻辑说明

### 检查项创建优先级
1. **模板检查项**：从选择的模板中复制检查项
2. **自定义检查项**：添加用户自定义的检查项
3. **默认检查项**：如果既没有模板也没有自定义项，创建默认的3个检查项

### 排序规则
- 模板检查项保持原有排序
- 自定义检查项使用用户指定的排序
- 如果自定义项没有指定排序，自动分配递增的排序号

### 默认检查项
如果用户既不选择模板也不提供自定义检查项，系统会自动创建：
1. 基础设施检查
2. 安全设施检查  
3. 环境质量检查

## 📱 移动端UI建议

### 简化创建界面
```
┌─────────────────────────────────┐
│ 创建新项目                        │
├─────────────────────────────────┤
│ 项目标题 *                       │
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 检查清单名称 *                   │
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 选择模板（可选）:                 │
│ ☐ 标准检查清单 (5项)             │
│ ☑ 安全检查清单 (3项)             │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        创建项目              │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## 🧪 测试数据

### 基础测试
```json
{
  "projectTitle": "测试项目1",
  "checklistName": "测试检查清单1"
}
```

### 模板测试
```json
{
  "projectTitle": "测试项目2",
  "checklistName": "测试检查清单2",
  "templateIds": [1]
}
```

### 自定义测试
```json
{
  "projectTitle": "测试项目3",
  "checklistName": "测试检查清单3",
  "customChecklistItems": [
    {
      "title": "自定义检查项",
      "sortOrder": 1
    }
  ]
}
```

### 混合测试
```json
{
  "projectTitle": "测试项目4",
  "checklistName": "测试检查清单4",
  "templateIds": [1, 2],
  "customChecklistItems": [
    {
      "title": "额外检查项1",
      "sortOrder": 10
    },
    {
      "title": "额外检查项2",
      "sortOrder": 11
    }
  ]
}
```

## ⚠️ 错误处理

### 常见错误
- `项目标题不能为空`
- `检查清单名称不能为空`
- `模板不存在`
- `模板不属于当前公司`

### 错误响应格式
```json
{
  "Response": {
    "Code": "100",
    "Message": "具体错误描述"
  }
}
```

## 🚀 快速开始

1. **获取模板列表**（可选）
2. **创建项目**，根据需要选择：
   - 只填写标题和检查清单名称（使用默认检查项）
   - 选择一个或多个模板
   - 添加自定义检查项
   - 或者两者结合

这个简化版设计大大减少了用户的输入负担，同时保持了足够的灵活性！
