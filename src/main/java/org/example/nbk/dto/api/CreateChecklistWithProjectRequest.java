package org.example.nbk.dto.api;

import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class CreateChecklistWithProjectRequest {
    
    // 项目基本信息
    @NotBlank(message = "项目标题不能为空")
    private String projectTitle;
    
    private String projectDescription;
    private String address;
    private String postNo;
    private String poststed;
    private String kommune;
    private String comments;
    private Integer customerId;
    private Integer contactPersonId;
    private String longitude;
    private String latitude;
    
    // 检查清单信息
    @NotBlank(message = "检查清单名称不能为空")
    private String checklistName;
    
    private String checklistComment;
    
    @NotNull(message = "检查清单项不能为空")
    private List<CreateChecklistItemRequest> checklistItems = new ArrayList<>();
}
