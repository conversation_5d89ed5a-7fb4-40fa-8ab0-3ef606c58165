package org.example.nbk.dto.api;

import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

@Data
@NoArgsConstructor
public class CreateChecklistFromTemplateRequest {

    // 项目基本信息 - 只需要标题
    @NotBlank(message = "项目标题不能为空")
    private String projectTitle;

    // 可选的项目信息
    private String projectDescription;
    private String address;
    private String postNo;
    private String poststed;
    private String kommune;
    private String comments;
    private Integer customerId;
    private Integer contactPersonId;
    private String longitude;
    private String latitude;

    // 检查清单模板信息 - 支持多个模板ID
    @NotEmpty(message = "至少需要选择一个检查清单模板")
    private List<Integer> templateIds;

    private String checklistName; // 可选，如果不提供则使用模板名称
    private String checklistComment;
}
