package org.example.nbk.dto.api;

import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class CreateChecklistFromTemplateRequest {
    
    // 项目基本信息
    @NotBlank(message = "项目标题不能为空")
    private String projectTitle;
    
    private String projectDescription;
    private String address;
    private String postNo;
    private String poststed;
    private String kommune;
    private String comments;
    private Integer customerId;
    private Integer contactPersonId;
    private String longitude;
    private String latitude;
    
    // 检查清单模板信息
    @NotNull(message = "检查清单模板ID不能为空")
    private Integer templateId;
    
    private String checklistName; // 可选，如果不提供则使用模板名称
    private String checklistComment;
}
