package org.example.nbk.dto.api;

import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 通过检查清单创建项目的请求DTO
 */
@Data
@NoArgsConstructor
public class CreateProjectWithChecklistRequest {
    
    /**
     * 项目基本信息
     */
    @NotBlank(message = "项目标题不能为空")
    private String projectTitle;
    
    private String projectDescription;
    
    private String address;
    
    private String postNo;
    
    private String poststed;
    
    private String kommune;
    
    private String comments;
    
    /**
     * 客户信息（可选，如果不提供则创建默认客户）
     */
    private Integer customerId;
    
    private Integer contactPersonId;
    
    /**
     * 位置信息（可选）
     */
    private String longitude;
    
    private String latitude;
    
    /**
     * 检查清单信息
     */
    @NotNull(message = "必须提供检查清单信息")
    private ChecklistCreationInfo checklistInfo;
    
    /**
     * 检查清单创建信息
     */
    @Data
    @NoArgsConstructor
    public static class ChecklistCreationInfo {
        
        /**
         * 检查清单名称
         */
        @NotBlank(message = "检查清单名称不能为空")
        private String checklistName;
        
        /**
         * 关联的服务ID（可选）
         */
        private Integer serviceId;
        
        /**
         * 检查清单模板ID（可选，如果提供则从模板创建）
         */
        private Integer templateId;
        
        /**
         * 自定义检查项列表（如果不使用模板）
         */
        private List<ChecklistItemInfo> customItems;
        
        /**
         * 检查清单数量（默认为1）
         */
        private Integer quantity = 1;
    }
    
    /**
     * 检查项信息
     */
    @Data
    @NoArgsConstructor
    public static class ChecklistItemInfo {
        
        @NotBlank(message = "检查项标题不能为空")
        private String title;
        
        private Integer sortOrder;
    }
}
