package org.example.nbk.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.nbk.dto.api.*;
import org.example.nbk.entity.User;
import org.example.nbk.service.MobileAppService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(MobileAppController.class)
class MobileAppControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MobileAppService mobileAppService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1);
        testUser.setCompanyID(1);
        testUser.setUsername("testuser");
    }

    @Test
    void testGetChecklistTemplates() throws Exception {
        // Arrange
        ChecklistTemplateContainer mockResponse = new ChecklistTemplateContainer();
        ChecklistTemplateDto template = new ChecklistTemplateDto();
        template.setId(1);
        template.setTitle("Test Template");
        template.setIsDefault(true);
        
        ChecklistItemTemplateDto item = new ChecklistItemTemplateDto();
        item.setId(1);
        item.setTitle("Test Item");
        template.setItems(Arrays.asList(item));
        
        mockResponse.setListOfTemplates(Arrays.asList(template));
        mockResponse.setResponse(new Response("200", "Success"));

        when(mobileAppService.getChecklistTemplates(anyInt())).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/MobileApp/GetChecklistTemplates"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.Response.Code").value("200"))
                .andExpect(jsonPath("$.ListOfTemplates[0].id").value(1))
                .andExpect(jsonPath("$.ListOfTemplates[0].title").value("Test Template"));
    }

    @Test
    void testCreateChecklistWithProject() throws Exception {
        // Arrange
        CreateChecklistWithProjectRequest request = new CreateChecklistWithProjectRequest();
        request.setProjectTitle("Test Project");
        request.setProjectDescription("Test Description");
        request.setChecklistName("Test Checklist");
        
        CreateChecklistItemRequest item = new CreateChecklistItemRequest();
        item.setTitle("Test Item");
        item.setSortOrder(1);
        request.setChecklistItems(Arrays.asList(item));

        CreateChecklistWithProjectResponse mockResponse = new CreateChecklistWithProjectResponse();
        CreateChecklistWithProjectData data = new CreateChecklistWithProjectData();
        data.setProjectId(1);
        data.setProjectTitle("Test Project");
        data.setChecklistId(1);
        data.setChecklistName("Test Checklist");
        mockResponse.setData(data);
        mockResponse.setResponse(new Response("200", "Success"));

        when(mobileAppService.createChecklistWithProject(any(), anyInt(), anyInt())).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(post("/api/MobileApp/CreateChecklistWithProject")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.Response.Code").value("200"))
                .andExpect(jsonPath("$.Data.projectId").value(1))
                .andExpect(jsonPath("$.Data.projectTitle").value("Test Project"));
    }

    @Test
    void testCreateChecklistFromTemplate() throws Exception {
        // Arrange
        CreateChecklistFromTemplateRequest request = new CreateChecklistFromTemplateRequest();
        request.setProjectTitle("Test Project from Template");
        request.setTemplateId(1);

        CreateChecklistWithProjectResponse mockResponse = new CreateChecklistWithProjectResponse();
        CreateChecklistWithProjectData data = new CreateChecklistWithProjectData();
        data.setProjectId(2);
        data.setProjectTitle("Test Project from Template");
        data.setChecklistId(2);
        data.setChecklistName("Template Checklist");
        mockResponse.setData(data);
        mockResponse.setResponse(new Response("200", "Success"));

        when(mobileAppService.createChecklistFromTemplate(any(), anyInt(), anyInt())).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(post("/api/MobileApp/CreateChecklistFromTemplate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.Response.Code").value("200"))
                .andExpect(jsonPath("$.Data.projectId").value(2))
                .andExpect(jsonPath("$.Data.projectTitle").value("Test Project from Template"));
    }
}
