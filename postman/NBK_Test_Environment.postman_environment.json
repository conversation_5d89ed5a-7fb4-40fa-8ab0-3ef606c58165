{"id": "12345678-1234-1234-1234-123456789def", "name": "NBK Test Environment", "values": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "token", "value": "", "type": "secret", "enabled": true}, {"key": "username", "value": "testuser", "type": "default", "enabled": true}, {"key": "password", "value": "testpassword", "type": "secret", "enabled": true}, {"key": "companyId", "value": "1", "type": "default", "enabled": true}, {"key": "customerId", "value": "1", "type": "default", "enabled": true}, {"key": "contactPersonId", "value": "1", "type": "default", "enabled": true}, {"key": "templateId", "value": "1", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-08-04T06:57:00.000Z", "_postman_exported_using": "Postman/10.0.0"}