{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "NBK Mobile App - Checklist APIs", "description": "移动APP检查清单相关API接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 获取检查清单模板列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetChecklistTemplates", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetChecklistTemplates"]}, "description": "获取公司的检查清单模板列表，供用户选择使用"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetChecklistTemplates", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetChecklistTemplates"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"Response\": {\n    \"Code\": \"200\",\n    \"Message\": \"Success\"\n  },\n  \"ListOfTemplates\": [\n    {\n      \"id\": 1,\n      \"title\": \"标准检查清单模板\",\n      \"isDefault\": true,\n      \"sortOrder\": 1,\n      \"items\": [\n        {\n          \"id\": 1,\n          \"title\": \"基础设施检查\",\n          \"sortOrder\": 1\n        },\n        {\n          \"id\": 2,\n          \"title\": \"安全设施检查\",\n          \"sortOrder\": 2\n        }\n      ]\n    }\n  ]\n}"}]}, {"name": "2. 创建检查清单并自动生成项目", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"移动APP测试项目\",\n  \"projectDescription\": \"这是一个通过移动APP创建的测试项目\",\n  \"address\": \"北京市朝阳区测试街道123号\",\n  \"postNo\": \"100000\",\n  \"poststed\": \"北京\",\n  \"kommune\": \"朝阳区\",\n  \"comments\": \"移动APP自动创建项目测试\",\n  \"customerId\": 1,\n  \"contactPersonId\": 1,\n  \"longitude\": \"116.4074\",\n  \"latitude\": \"39.9042\",\n  \"checklistName\": \"移动APP测试检查清单\",\n  \"checklistComment\": \"通过移动APP创建的检查清单\",\n  \"checklistItems\": [\n    {\n      \"title\": \"检查项目1：基础设施检查\",\n      \"sortOrder\": 1\n    },\n    {\n      \"title\": \"检查项目2：安全设施检查\",\n      \"sortOrder\": 2\n    },\n    {\n      \"title\": \"检查项目3：环境质量检查\",\n      \"sortOrder\": 3\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistWithProject", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistWithProject"]}, "description": "用户提供项目信息和检查清单详情，系统自动创建项目和检查清单"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"移动APP测试项目\",\n  \"checklistName\": \"移动APP测试检查清单\",\n  \"checklistItems\": [\n    {\n      \"title\": \"检查项目1：基础设施检查\",\n      \"sortOrder\": 1\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistWithProject", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistWithProject"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"Response\": {\n    \"Code\": \"200\",\n    \"Message\": \"Success\"\n  },\n  \"Data\": {\n    \"projectId\": 123,\n    \"projectTitle\": \"移动APP测试项目\",\n    \"checklistId\": 456,\n    \"checklistName\": \"移动APP测试检查清单\",\n    \"checklistItems\": [\n      {\n        \"id\": 789,\n        \"title\": \"检查项目1：基础设施检查\",\n        \"sortOrder\": 1,\n        \"status\": null\n      }\n    ]\n  }\n}"}]}, {"name": "3. 基于模板创建检查清单并自动生成项目", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"基于模板的测试项目\",\n  \"projectDescription\": \"使用检查清单模板创建的项目\",\n  \"address\": \"上海市浦东新区模板测试路456号\",\n  \"postNo\": \"200000\",\n  \"poststed\": \"上海\",\n  \"kommune\": \"浦东新区\",\n  \"comments\": \"基于模板创建的测试项目\",\n  \"customerId\": 1,\n  \"contactPersonId\": 1,\n  \"longitude\": \"121.4737\",\n  \"latitude\": \"31.2304\",\n  \"templateId\": 1,\n  \"checklistName\": \"自定义模板检查清单名称\",\n  \"checklistComment\": \"基于模板创建的检查清单\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistFromTemplate", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistFromTemplate"]}, "description": "用户选择模板并提供项目信息，系统基于模板自动创建项目和检查清单"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"基于模板的测试项目\",\n  \"templateId\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistFromTemplate", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistFromTemplate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"Response\": {\n    \"Code\": \"200\",\n    \"Message\": \"Success\"\n  },\n  \"Data\": {\n    \"projectId\": 124,\n    \"projectTitle\": \"基于模板的测试项目\",\n    \"checklistId\": 457,\n    \"checklistName\": \"标准检查清单模板\",\n    \"checklistItems\": [\n      {\n        \"id\": 790,\n        \"title\": \"模板检查项目1\",\n        \"sortOrder\": 1,\n        \"status\": null\n      },\n      {\n        \"id\": 791,\n        \"title\": \"模板检查项目2\",\n        \"sortOrder\": 2,\n        \"status\": null\n      }\n    ]\n  }\n}"}]}, {"name": "4. 获取项目列表 (现有接口)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetProjectList", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetProjectList"]}, "description": "获取检查员的项目列表（用于验证新创建的项目）"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}]}