{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "NBK Mobile App - Simplified Checklist APIs", "description": "移动APP检查清单API - 简化版（只有2个接口）", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 获取检查清单模板列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetChecklistTemplates", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetChecklistTemplates"]}, "description": "获取公司的检查清单模板列表，供用户选择使用"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetChecklistTemplates", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetChecklistTemplates"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"Response\": {\n    \"Code\": \"200\",\n    \"Message\": \"Success\"\n  },\n  \"ListOfTemplates\": [\n    {\n      \"id\": 1,\n      \"title\": \"标准检查清单模板\",\n      \"isDefault\": true,\n      \"sortOrder\": 1,\n      \"items\": [\n        {\n          \"id\": 1,\n          \"title\": \"基础设施检查\",\n          \"sortOrder\": 1\n        },\n        {\n          \"id\": 2,\n          \"title\": \"安全设施检查\",\n          \"sortOrder\": 2\n        }\n      ]\n    }\n  ]\n}"}]}, {"name": "2. 基于模板创建检查清单并自动生成项目", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"基于模板的测试项目\",\n  \"checklistName\": \"我的检查清单\",\n  \"templateIds\": [1, 2],\n  \"customChecklistItems\": [\n    {\n      \"title\": \"自定义检查项1\",\n      \"sortOrder\": 10\n    },\n    {\n      \"title\": \"自定义检查项2\",\n      \"sortOrder\": 11\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistFromTemplate", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistFromTemplate"]}, "description": "用户选择模板和/或自定义检查项，系统自动创建项目和检查清单"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"projectTitle\": \"基于模板的测试项目\",\n  \"templateId\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/MobileApp/CreateChecklistFromTemplate", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "CreateChecklistFromTemplate"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"Response\": {\n    \"Code\": \"200\",\n    \"Message\": \"Success\"\n  },\n  \"Data\": {\n    \"projectId\": 124,\n    \"projectTitle\": \"基于模板的测试项目\",\n    \"checklistId\": 457,\n    \"checklistName\": \"标准检查清单模板\",\n    \"checklistItems\": [\n      {\n        \"id\": 790,\n        \"title\": \"模板检查项目1\",\n        \"sortOrder\": 1,\n        \"status\": null\n      },\n      {\n        \"id\": 791,\n        \"title\": \"模板检查项目2\",\n        \"sortOrder\": 2,\n        \"status\": null\n      }\n    ]\n  }\n}"}]}, {"name": "3. 获取项目列表 (验证创建结果)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/MobileApp/GetProjectList", "host": ["{{baseUrl}}"], "path": ["api", "MobileApp", "GetProjectList"]}, "description": "获取检查员的项目列表（用于验证新创建的项目）"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}]}