# NBK移动APP检查清单API - Postman测试指南

## 📁 文件说明

- `NBK_MobileApp_Checklist_APIs.postman_collection.json` - API接口集合
- `NBK_Test_Environment.postman_environment.json` - 测试环境配置
- `测试指南.md` - 本文件，详细测试说明

## 🎯 新增API概述

我们新增了4个API接口，专为移动APP设计：

1. **获取模板列表** - `GET /api/MobileApp/GetChecklistTemplates`
2. **快速创建项目** - `POST /api/MobileApp/QuickCreateProject` ⭐ **推荐**
3. **自定义创建项目** - `POST /api/MobileApp/CreateChecklistWithProject`
4. **基于模板创建** - `POST /api/MobileApp/CreateChecklistFromTemplate`

## 🚀 导入步骤

### 1. 导入Collection
1. 打开Postman
2. 点击左上角 "Import" 按钮
3. 选择 `NBK_MobileApp_Checklist_APIs.postman_collection.json` 文件
4. 点击 "Import" 完成导入

### 2. 导入Environment
1. 点击右上角齿轮图标 "Manage Environments"
2. 点击 "Import" 按钮
3. 选择 `NBK_Test_Environment.postman_environment.json` 文件
4. 点击 "Import" 完成导入
5. 选择 "NBK Test Environment" 作为当前环境

## ⚙️ 环境配置

在测试前，请在Environment中配置以下变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `baseUrl` | API服务器地址 | `http://localhost:8080` |
| `token` | JWT认证令牌 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `username` | 测试用户名 | `testuser` |
| `password` | 测试密码 | `testpassword` |
| `companyId` | 公司ID | `1` |
| `customerId` | 客户ID | `1` |
| `contactPersonId` | 联系人ID | `1` |
| `templateId` | 模板ID | `1` |

## 🔐 获取认证Token

在测试新接口前，您需要先获取认证token：

1. 使用现有的登录接口获取token
2. 将获取到的token设置到Environment的`token`变量中
3. 或者在每个请求的Authorization header中手动设置

## 📋 测试流程

### 🚀 推荐测试顺序：

#### 1️⃣ 获取检查清单模板列表
- **接口：** `GET /api/MobileApp/GetChecklistTemplates`
- **目的：** 查看系统中可用的检查清单模板
- **测试数据：** 无需请求体
- **预期结果：** 返回模板列表，记录模板ID用于后续测试

#### 2️⃣ 快速创建项目（极简版）⭐ **重点测试**
- **接口：** `POST /api/MobileApp/QuickCreateProject`
- **目的：** 测试最简化的项目创建流程
- **测试数据：**
  ```json
  {
    "projectTitle": "快速测试项目"
  }
  ```
- **预期结果：** 成功创建项目和默认检查清单

#### 3️⃣ 快速创建项目（带模板选择）
- **接口：** `POST /api/MobileApp/QuickCreateProject`
- **目的：** 测试选择模板的项目创建
- **测试数据：**
  ```json
  {
    "projectTitle": "模板测试项目",
    "templateIds": [1, 2]
  }
  ```
- **预期结果：** 基于选择的模板创建项目和检查清单

#### 4️⃣ 自定义检查清单项目
- **接口：** `POST /api/MobileApp/CreateChecklistWithProject`
- **目的：** 测试用户完全自定义检查清单
- **测试数据：**
  ```json
  {
    "projectTitle": "自定义测试项目",
    "checklistName": "自定义检查清单",
    "checklistItems": [
      {
        "title": "自定义检查项1",
        "sortOrder": 1
      }
    ]
  }
  ```

#### 5️⃣ 验证创建结果
- **接口：** `GET /api/MobileApp/GetProjectList`
- **目的：** 验证所有新创建的项目都出现在列表中
- **预期结果：** 列表中包含所有新创建的项目

## 🧪 详细测试数据

### 1. 快速创建项目测试数据

#### 极简测试（只有标题）
```json
{
  "projectTitle": "极简测试项目"
}
```

#### 带描述测试
```json
{
  "projectTitle": "带描述的测试项目",
  "projectDescription": "这是一个包含描述的测试项目"
}
```

#### 单模板测试
```json
{
  "projectTitle": "单模板测试项目",
  "templateIds": [1]
}
```

#### 多模板测试
```json
{
  "projectTitle": "多模板测试项目",
  "templateIds": [1, 2, 3]
}
```

### 2. 自定义检查清单测试数据

#### 基础自定义
```json
{
  "projectTitle": "自定义检查清单项目",
  "checklistName": "我的检查清单",
  "checklistItems": [
    {
      "title": "检查项目1",
      "sortOrder": 1
    },
    {
      "title": "检查项目2",
      "sortOrder": 2
    }
  ]
}
```

#### 完整自定义
```json
{
  "projectTitle": "完整自定义项目",
  "projectDescription": "详细的项目描述",
  "address": "北京市朝阳区测试街道123号",
  "checklistName": "完整检查清单",
  "checklistComment": "检查清单备注",
  "checklistItems": [
    {
      "title": "基础设施检查",
      "sortOrder": 1
    },
    {
      "title": "安全设施检查",
      "sortOrder": 2
    },
    {
      "title": "环境质量检查",
      "sortOrder": 3
    }
  ]
}
```

### 3. 基于模板创建测试数据

#### 单模板
```json
{
  "projectTitle": "基于单模板的项目",
  "templateIds": [1]
}
```

#### 多模板
```json
{
  "projectTitle": "基于多模板的项目",
  "templateIds": [1, 2, 3],
  "checklistName": "组合检查清单"
}
```

### 4. 边界测试数据

#### 空模板数组
```json
{
  "projectTitle": "空模板测试",
  "templateIds": []
}
```

#### 长标题测试
```json
{
  "projectTitle": "这是一个非常长的项目标题用来测试系统对长标题的处理能力和数据库字段长度限制"
}
```

## ✅ 预期响应格式

### 成功响应
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "项目标题",
    "checklistId": 456,
    "checklistName": "检查清单名称",
    "checklistItems": [...]
  }
}
```

### 错误响应
```json
{
  "Response": {
    "Code": "100",
    "Message": "错误描述信息"
  }
}
```

## 🐛 常见问题排查

### 1. 认证失败 (401)
- 检查token是否正确设置
- 确认token是否过期
- 验证Authorization header格式

### 2. 权限不足 (403)
- 确认用户有相应权限
- 检查公司ID是否正确

### 3. 参数验证失败 (400)
- 检查必填字段是否提供
- 验证数据格式是否正确
- 确认ID字段是否存在

### 4. 服务器错误 (500)
- 检查服务器日志
- 确认数据库连接正常
- 验证相关服务是否启动

## 📊 完整测试检查清单

### ✅ 基础功能测试
- [ ] 成功获取检查清单模板列表
- [ ] 快速创建项目（只有标题）
- [ ] 快速创建项目（带模板选择）
- [ ] 自定义检查清单项目创建
- [ ] 基于模板创建项目
- [ ] 验证所有项目出现在项目列表中

### ✅ 数据验证测试
- [ ] 项目标题必填验证
- [ ] 模板ID存在性验证
- [ ] 模板权限验证（只能使用本公司模板）
- [ ] 响应数据格式正确性
- [ ] 创建的检查项数量正确

### ✅ 边界情况测试
- [ ] 空模板数组处理
- [ ] 不存在的模板ID处理
- [ ] 超长标题处理
- [ ] 特殊字符标题处理
- [ ] 无认证token访问

### ✅ 错误处理测试
- [ ] 认证失败处理
- [ ] 权限不足处理
- [ ] 参数验证失败处理
- [ ] 服务器错误处理
- [ ] 事务回滚验证

### ✅ 性能测试
- [ ] 单个项目创建响应时间
- [ ] 多模板项目创建响应时间
- [ ] 并发创建项目测试
- [ ] 大量检查项创建测试

## 💡 测试建议

1. **先测试GET接口**：从获取模板列表开始，确保基础功能正常
2. **逐步测试POST接口**：先测试简单的创建，再测试复杂场景
3. **验证数据一致性**：创建后检查数据库中的数据是否正确
4. **测试边界情况**：空值、超长字符串、不存在的ID等
5. **并发测试**：多个用户同时创建项目的情况

## 📞 技术支持

如果在测试过程中遇到问题，请检查：
1. 服务器是否正常运行
2. 数据库连接是否正常
3. 相关依赖服务是否启动
4. 日志文件中的错误信息
