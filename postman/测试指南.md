# NBK移动APP检查清单API测试指南

## 📁 文件说明

- `NBK_MobileApp_Checklist_APIs.postman_collection.json` - API接口集合
- `NBK_Test_Environment.postman_environment.json` - 测试环境配置
- `测试指南.md` - 本文件，详细测试说明

## 🚀 导入步骤

### 1. 导入Collection
1. 打开Postman
2. 点击左上角 "Import" 按钮
3. 选择 `NBK_MobileApp_Checklist_APIs.postman_collection.json` 文件
4. 点击 "Import" 完成导入

### 2. 导入Environment
1. 点击右上角齿轮图标 "Manage Environments"
2. 点击 "Import" 按钮
3. 选择 `NBK_Test_Environment.postman_environment.json` 文件
4. 点击 "Import" 完成导入
5. 选择 "NBK Test Environment" 作为当前环境

## ⚙️ 环境配置

在测试前，请在Environment中配置以下变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `baseUrl` | API服务器地址 | `http://localhost:8080` |
| `token` | JWT认证令牌 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `username` | 测试用户名 | `testuser` |
| `password` | 测试密码 | `testpassword` |
| `companyId` | 公司ID | `1` |
| `customerId` | 客户ID | `1` |
| `contactPersonId` | 联系人ID | `1` |
| `templateId` | 模板ID | `1` |

## 🔐 获取认证Token

在测试新接口前，您需要先获取认证token：

1. 使用现有的登录接口获取token
2. 将获取到的token设置到Environment的`token`变量中
3. 或者在每个请求的Authorization header中手动设置

## 📋 测试流程

### 推荐测试顺序：

#### 1️⃣ 获取检查清单模板列表
- **接口：** `GET /api/MobileApp/GetChecklistTemplates`
- **目的：** 查看系统中可用的检查清单模板
- **预期结果：** 返回模板列表，记录模板ID用于后续测试

#### 2️⃣ 创建自定义检查清单项目
- **接口：** `POST /api/MobileApp/CreateChecklistWithProject`
- **目的：** 测试用户自定义检查清单并自动创建项目
- **预期结果：** 成功创建项目和检查清单，返回相关ID

#### 3️⃣ 基于模板创建项目
- **接口：** `POST /api/MobileApp/CreateChecklistFromTemplate`
- **目的：** 测试基于模板创建项目和检查清单
- **注意：** 使用步骤1中获取的模板ID
- **预期结果：** 成功创建项目和检查清单

#### 4️⃣ 验证创建结果
- **接口：** `GET /api/MobileApp/GetProjectList`
- **目的：** 验证新创建的项目是否出现在项目列表中
- **预期结果：** 列表中包含新创建的项目

## 🧪 测试数据说明

### 必填字段测试
确保以下字段不能为空：
- `projectTitle` - 项目标题
- `checklistName` - 检查清单名称（自定义方式）
- `templateId` - 模板ID（模板方式）
- `checklistItems` - 检查项列表（自定义方式，至少一项）

### 可选字段测试
以下字段可以为空或不传：
- `projectDescription` - 项目描述
- `address` - 地址
- `postNo` - 邮编
- `customerId` - 客户ID
- `longitude`/`latitude` - 坐标
- `checklistComment` - 检查清单备注

## ✅ 预期响应格式

### 成功响应
```json
{
  "Response": {
    "Code": "200",
    "Message": "Success"
  },
  "Data": {
    "projectId": 123,
    "projectTitle": "项目标题",
    "checklistId": 456,
    "checklistName": "检查清单名称",
    "checklistItems": [...]
  }
}
```

### 错误响应
```json
{
  "Response": {
    "Code": "100",
    "Message": "错误描述信息"
  }
}
```

## 🐛 常见问题排查

### 1. 认证失败 (401)
- 检查token是否正确设置
- 确认token是否过期
- 验证Authorization header格式

### 2. 权限不足 (403)
- 确认用户有相应权限
- 检查公司ID是否正确

### 3. 参数验证失败 (400)
- 检查必填字段是否提供
- 验证数据格式是否正确
- 确认ID字段是否存在

### 4. 服务器错误 (500)
- 检查服务器日志
- 确认数据库连接正常
- 验证相关服务是否启动

## 📊 测试检查清单

- [ ] 成功获取检查清单模板列表
- [ ] 成功创建自定义检查清单项目
- [ ] 成功基于模板创建项目
- [ ] 验证项目出现在项目列表中
- [ ] 测试必填字段验证
- [ ] 测试错误情况处理
- [ ] 验证响应数据格式正确
- [ ] 确认事务回滚机制工作正常

## 💡 测试建议

1. **先测试GET接口**：从获取模板列表开始，确保基础功能正常
2. **逐步测试POST接口**：先测试简单的创建，再测试复杂场景
3. **验证数据一致性**：创建后检查数据库中的数据是否正确
4. **测试边界情况**：空值、超长字符串、不存在的ID等
5. **并发测试**：多个用户同时创建项目的情况

## 📞 技术支持

如果在测试过程中遇到问题，请检查：
1. 服务器是否正常运行
2. 数据库连接是否正常
3. 相关依赖服务是否启动
4. 日志文件中的错误信息
